package com.example.flutter_schedule_augment

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

class SmartScheduleWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onAppWidgetOptionsChanged(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        newOptions: Bundle
    ) {
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions)
        updateAppWidget(context, appWidgetManager, appWidgetId)
    }

    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        // Determine widget size and use appropriate layout
        val options = appWidgetManager.getAppWidgetOptions(appWidgetId)
        val minWidth = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_WIDTH)
        val minHeight = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_HEIGHT)
        
        val layoutId = getLayoutForSize(minWidth, minHeight)
        val views = RemoteViews(context.packageName, layoutId)
        
        try {
            // Get schedule data from shared preferences
            val widgetData = HomeWidgetPlugin.getData(context)
            val scheduleData = widgetData.getString("upcoming_schedules", null)
            
            if (scheduleData != null && scheduleData.isNotEmpty()) {
                val schedules = JSONArray(scheduleData)
                if (schedules.length() > 0) {
                    updateWithScheduleData(views, schedules, layoutId, context)
                } else {
                    showEmptyState(views, layoutId)
                }
            } else {
                showEmptyState(views, layoutId)
            }
        } catch (e: Exception) {
            showEmptyState(views, layoutId)
        }
        
        // Set up click intent to open the app
        setupClickIntents(views, context, layoutId)
        
        appWidgetManager.updateAppWidget(appWidgetId, views)
    }
    
    private fun getLayoutForSize(width: Int, height: Int): Int {
        return when {
            width < 180 || height < 110 -> R.layout.schedule_widget_small
            width > 300 || height > 200 -> R.layout.schedule_widget_large
            else -> R.layout.schedule_widget_simple
        }
    }
    
    private fun updateWithScheduleData(views: RemoteViews, schedules: JSONArray, layoutId: Int, context: Context) {
        when (layoutId) {
            R.layout.schedule_widget_small -> updateSmallWidget(views, schedules, context)
            R.layout.schedule_widget_large -> updateLargeWidget(views, schedules, context)
            else -> updateMediumWidget(views, schedules, context)
        }
    }
    
    private fun updateSmallWidget(views: RemoteViews, schedules: JSONArray, context: Context) {
        try {
            views.setViewVisibility(R.id.small_widget_content, android.view.View.VISIBLE)
            views.setViewVisibility(R.id.small_empty_message, android.view.View.GONE)
            
            val schedule = schedules.getJSONObject(0)
            val title = schedule.getString("title")
            val startTime = schedule.getLong("startDateTime")
            val type = schedule.getInt("type")
            val colorCode = schedule.getString("colorCode")
            
            // Format time
            val date = Date(startTime)
            val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
            val formattedTime = timeFormat.format(date)
            
            // Update views
            views.setTextViewText(R.id.small_event_time, formattedTime)
            views.setTextViewText(R.id.small_event_title, title)
            views.setTextViewText(R.id.small_event_type, getTypeName(type))
            
            // Set color indicators
            val color = parseColor(colorCode, type)
            views.setInt(R.id.small_type_indicator, "setBackgroundColor", color)
            views.setInt(R.id.header_indicator, "setBackgroundColor", color)
            
        } catch (e: Exception) {
            showEmptyState(views, R.layout.schedule_widget_small)
        }
    }
    
    private fun updateMediumWidget(views: RemoteViews, schedules: JSONArray, context: Context) {
        try {
            // Show content, hide empty state
            views.setViewVisibility(R.id.widget_content, android.view.View.VISIBLE)
            views.setViewVisibility(R.id.empty_message, android.view.View.GONE)
            
            // Update first event
            if (schedules.length() > 0) {
                updateEventItem(views, schedules.getJSONObject(0), 1)
                views.setViewVisibility(R.id.event_1, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.event_1, android.view.View.GONE)
            }
            
            // Update second event
            if (schedules.length() > 1) {
                updateEventItem(views, schedules.getJSONObject(1), 2)
                views.setViewVisibility(R.id.event_2, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.event_2, android.view.View.GONE)
            }
            
            // Show more events indicator
            if (schedules.length() > 2) {
                val remainingCount = schedules.length() - 2
                views.setTextViewText(R.id.more_events_indicator, "+ $remainingCount more events")
                views.setViewVisibility(R.id.more_events_indicator, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.more_events_indicator, android.view.View.GONE)
            }
            
        } catch (e: Exception) {
            showEmptyState(views, R.layout.schedule_widget_simple)
        }
    }
    
    private fun updateLargeWidget(views: RemoteViews, schedules: JSONArray, context: Context) {
        try {
            views.setViewVisibility(R.id.large_widget_content, android.view.View.VISIBLE)
            views.setViewVisibility(R.id.large_empty_message, android.view.View.GONE)
            
            // Update event count
            views.setTextViewText(R.id.large_event_count, "${schedules.length()} events")
            
            // Update up to 3 events for large widget
            val maxEvents = minOf(schedules.length(), 3)
            val eventIds = arrayOf(R.id.large_event_1, R.id.large_event_2, R.id.large_event_3)
            
            for (i in 0 until maxEvents) {
                updateLargeEventItem(views, schedules.getJSONObject(i), i + 1)
                views.setViewVisibility(eventIds[i], android.view.View.VISIBLE)
            }
            
            // Hide unused event slots
            for (i in maxEvents until eventIds.size) {
                views.setViewVisibility(eventIds[i], android.view.View.GONE)
            }
            
            // Show more events indicator
            if (schedules.length() > 3) {
                val remainingCount = schedules.length() - 3
                views.setTextViewText(R.id.large_more_events, "+ $remainingCount more events")
                views.setViewVisibility(R.id.large_more_events, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.large_more_events, android.view.View.GONE)
            }
            
        } catch (e: Exception) {
            showEmptyState(views, R.layout.schedule_widget_large)
        }
    }
    
    private fun updateEventItem(views: RemoteViews, schedule: JSONObject, eventNumber: Int) {
        try {
            val title = schedule.getString("title")
            val startTime = schedule.getLong("startDateTime")
            val type = schedule.getInt("type")
            val colorCode = schedule.getString("colorCode")
            
            // Format time
            val date = Date(startTime)
            val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
            val formattedTime = timeFormat.format(date)
            
            // Get view IDs based on event number
            val timeId = if (eventNumber == 1) R.id.event_1_time else R.id.event_2_time
            val titleId = if (eventNumber == 1) R.id.event_1_title else R.id.event_2_title
            val typeId = if (eventNumber == 1) R.id.event_1_type else R.id.event_2_type
            val indicatorId = if (eventNumber == 1) R.id.event_1_indicator else R.id.event_2_indicator
            
            // Update views
            views.setTextViewText(timeId, formattedTime)
            views.setTextViewText(titleId, title)
            views.setTextViewText(typeId, getTypeName(type))
            
            // Set color indicator
            val color = parseColor(colorCode, type)
            views.setInt(indicatorId, "setBackgroundColor", color)
            
        } catch (e: Exception) {
            // Handle error silently
        }
    }
    
    private fun updateLargeEventItem(views: RemoteViews, schedule: JSONObject, eventNumber: Int) {
        try {
            val title = schedule.getString("title")
            val description = schedule.getString("description")
            val startTime = schedule.getLong("startDateTime")
            val type = schedule.getInt("type")
            val colorCode = schedule.getString("colorCode")
            
            // Format time
            val date = Date(startTime)
            val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
            val formattedTime = timeFormat.format(date)
            
            // Get view IDs based on event number
            val timeId = when (eventNumber) {
                1 -> R.id.large_event_1_time
                2 -> R.id.large_event_2_time
                3 -> R.id.large_event_3_time
                else -> return
            }
            val titleId = when (eventNumber) {
                1 -> R.id.large_event_1_title
                2 -> R.id.large_event_2_title
                3 -> R.id.large_event_3_title
                else -> return
            }
            val descId = when (eventNumber) {
                1 -> R.id.large_event_1_desc
                2 -> R.id.large_event_2_desc
                3 -> R.id.large_event_3_desc
                else -> return
            }
            val typeId = when (eventNumber) {
                1 -> R.id.large_event_1_type
                2 -> R.id.large_event_2_type
                3 -> R.id.large_event_3_type
                else -> return
            }
            val indicatorId = when (eventNumber) {
                1 -> R.id.large_event_1_indicator
                2 -> R.id.large_event_2_indicator
                3 -> R.id.large_event_3_indicator
                else -> return
            }
            
            // Update views
            views.setTextViewText(timeId, formattedTime)
            views.setTextViewText(titleId, title)
            views.setTextViewText(descId, description)
            views.setTextViewText(typeId, getTypeName(type))
            
            // Set color indicator
            val color = parseColor(colorCode, type)
            views.setInt(indicatorId, "setBackgroundColor", color)
            
        } catch (e: Exception) {
            // Handle error silently
        }
    }
    
    private fun showEmptyState(views: RemoteViews, layoutId: Int) {
        when (layoutId) {
            R.layout.schedule_widget_small -> {
                views.setViewVisibility(R.id.small_widget_content, android.view.View.GONE)
                views.setViewVisibility(R.id.small_empty_message, android.view.View.VISIBLE)
            }
            R.layout.schedule_widget_large -> {
                views.setViewVisibility(R.id.large_widget_content, android.view.View.GONE)
                views.setViewVisibility(R.id.large_empty_message, android.view.View.VISIBLE)
            }
            else -> {
                views.setViewVisibility(R.id.widget_content, android.view.View.GONE)
                views.setViewVisibility(R.id.empty_message, android.view.View.VISIBLE)
            }
        }
    }
    
    private fun setupClickIntents(views: RemoteViews, context: Context, layoutId: Int) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // Set click intent on the appropriate container based on layout
        when (layoutId) {
            R.layout.schedule_widget_small -> {
                views.setOnClickPendingIntent(R.id.small_widget_content, pendingIntent)
                views.setOnClickPendingIntent(R.id.small_empty_message, pendingIntent)
            }
            R.layout.schedule_widget_large -> {
                views.setOnClickPendingIntent(R.id.large_widget_content, pendingIntent)
                views.setOnClickPendingIntent(R.id.large_empty_message, pendingIntent)
            }
            else -> {
                views.setOnClickPendingIntent(R.id.widget_content, pendingIntent)
                views.setOnClickPendingIntent(R.id.empty_message, pendingIntent)
            }
        }
    }
    
    private fun parseColor(colorCode: String, type: Int): Int {
        return try {
            android.graphics.Color.parseColor(colorCode)
        } catch (e: Exception) {
            // Fallback to type-based color
            getTypeColorInt(type)
        }
    }
    
    private fun getTypeColorInt(type: Int): Int {
        return when (type) {
            0 -> 0xFFFF6B6B.toInt() // Birthday
            1 -> 0xFFFF8E8E.toInt() // Anniversary
            2 -> 0xFF4ECDC4.toInt() // Holiday
            3 -> 0xFF45B7D1.toInt() // Personal
            4 -> 0xFF96CEB4.toInt() // Work
            5 -> 0xFFFFEAA7.toInt() // Meeting
            6 -> 0xFFDDA0DD.toInt() // Appointment
            7 -> 0xFFFFB347.toInt() // Reminder
            else -> 0xFFA8A8A8.toInt() // Other
        }
    }
    
    private fun getTypeName(type: Int): String {
        return when (type) {
            0 -> "Birthday"
            1 -> "Anniversary"
            2 -> "Holiday"
            3 -> "Personal"
            4 -> "Work"
            5 -> "Meeting"
            6 -> "Appointment"
            7 -> "Reminder"
            else -> "Other"
        }
    }

    private fun getDateText(date: Date): String {
        val now = Date()
        val today = Calendar.getInstance().apply { time = now }
        val scheduleDate = Calendar.getInstance().apply { time = date }

        val year = SimpleDateFormat("yyyy", Locale.getDefault()).format(date)

        return when {
            isSameDay(today, scheduleDate) -> "Today"
            isTomorrow(today, scheduleDate) -> "Tomorrow"
            isThisWeek(today, scheduleDate) -> SimpleDateFormat("EEE", Locale.getDefault()).format(date)
            else -> SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(date) // Include year
        }
    }

    private fun isSameDay(cal1: Calendar, cal2: Calendar): Boolean {
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }

    private fun isTomorrow(today: Calendar, date: Calendar): Boolean {
        val tomorrow = Calendar.getInstance().apply {
            time = today.time
            add(Calendar.DAY_OF_YEAR, 1)
        }
        return isSameDay(tomorrow, date)
    }

    private fun isThisWeek(today: Calendar, date: Calendar): Boolean {
        val startOfWeek = Calendar.getInstance().apply {
            time = today.time
            set(Calendar.DAY_OF_WEEK, firstDayOfWeek)
        }
        val endOfWeek = Calendar.getInstance().apply {
            time = startOfWeek.time
            add(Calendar.WEEK_OF_YEAR, 1)
        }
        return date.timeInMillis >= startOfWeek.timeInMillis &&
               date.timeInMillis < endOfWeek.timeInMillis
    }
}

