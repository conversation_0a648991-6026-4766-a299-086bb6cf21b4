<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="6dp"
    android:background="#FFFFFF">

    <!-- Widget Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="4dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Schedules"
            android:textSize="11sp"
            android:textStyle="bold"
            android:textColor="#212121"
            android:gravity="start" />

        <View
            android:id="@+id/header_indicator"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:background="#45B7D1" />

    </LinearLayout>

    <!-- Content -->
    <LinearLayout
        android:id="@+id/small_widget_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">

        <TextView
            android:id="@+id/small_event_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="10:00"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#212121" />

        <TextView
            android:id="@+id/small_event_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Meeting"
            android:textSize="10sp"
            android:textColor="#212121"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            android:paddingTop="2dp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingTop="2dp">

            <View
                android:id="@+id/small_type_indicator"
                android:layout_width="4dp"
                android:layout_height="4dp"
                android:background="#45B7D1" />

            <TextView
                android:id="@+id/small_event_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Work"
                android:textSize="8sp"
                android:textColor="#757575"
                android:paddingStart="4dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Empty State -->
    <TextView
        android:id="@+id/small_empty_message"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="No events"
        android:textSize="10sp"
        android:textColor="#757575"
        android:gravity="center"
        android:visibility="gone" />

</LinearLayout>
