<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="10dp"
    android:background="#FFFFFF">

    <!-- Widget Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Upcoming Schedules"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#212121" />

        <TextView
            android:id="@+id/large_event_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="5 events"
            android:textSize="10sp"
            android:textColor="#757575" />

    </LinearLayout>

    <!-- Content -->
    <LinearLayout
        android:id="@+id/large_widget_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Event 1 -->
        <LinearLayout
            android:id="@+id/large_event_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:id="@+id/large_event_1_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:00"
                android:textSize="13sp"
                android:textStyle="bold"
                android:textColor="#212121"
                android:minWidth="50dp" />

            <View
                android:id="@+id/large_event_1_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#45B7D1" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/large_event_1_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Team Meeting"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/large_event_1_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Weekly team sync meeting"
                    android:textSize="10sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end" />

            </LinearLayout>

            <TextView
                android:id="@+id/large_event_1_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Work"
                android:textSize="9sp"
                android:textColor="#757575"
                android:paddingStart="8dp" />

        </LinearLayout>

        <!-- Event 2 -->
        <LinearLayout
            android:id="@+id/large_event_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="?android:attr/selectableItemBackground"
            android:visibility="gone">

            <TextView
                android:id="@+id/large_event_2_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="14:00"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#212121"
                android:minWidth="50dp" />

            <View
                android:id="@+id/large_event_2_indicator"
                android:layout_width="4dp"
                android:layout_height="20dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FF6B6B" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/large_event_2_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Doctor Appointment"
                    android:textSize="11sp"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/large_event_2_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Annual checkup"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end" />

            </LinearLayout>

            <TextView
                android:id="@+id/large_event_2_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Personal"
                android:textSize="9sp"
                android:textColor="#757575"
                android:paddingStart="8dp" />

        </LinearLayout>

        <!-- Event 3 -->
        <LinearLayout
            android:id="@+id/large_event_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="?android:attr/selectableItemBackground"
            android:visibility="gone">

            <TextView
                android:id="@+id/large_event_3_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="16:30"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#212121"
                android:minWidth="50dp" />

            <View
                android:id="@+id/large_event_3_indicator"
                android:layout_width="4dp"
                android:layout_height="20dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#4ECDC4" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/large_event_3_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Birthday Party"
                    android:textSize="11sp"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/large_event_3_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="John's birthday celebration"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end" />

            </LinearLayout>

            <TextView
                android:id="@+id/large_event_3_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Personal"
                android:textSize="9sp"
                android:textColor="#757575"
                android:paddingStart="8dp" />

        </LinearLayout>

        <!-- More Events Indicator -->
        <TextView
            android:id="@+id/large_more_events"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="+ 2 more events today"
            android:textSize="10sp"
            android:textColor="#757575"
            android:gravity="center"
            android:padding="6dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Empty State -->
    <TextView
        android:id="@+id/large_empty_message"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="No upcoming schedules\nTap to add events"
        android:textSize="12sp"
        android:textColor="#757575"
        android:gravity="center"
        android:visibility="gone" />

</LinearLayout>
