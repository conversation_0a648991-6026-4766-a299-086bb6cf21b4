import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../models/schedule.dart';
import '../providers/schedule_provider.dart';
import '../screens/schedule_detail_screen.dart';

class ScheduleCard extends StatelessWidget {
  final Schedule schedule;
  final bool showDate;
  final VoidCallback? onTap;

  const ScheduleCard({
    super.key,
    required this.schedule,
    this.showDate = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    // Parse color from hex string
    Color scheduleColor;
    try {
      scheduleColor = Color(int.parse(schedule.colorCode.replaceFirst('#', '0xFF')));
    } catch (e) {
      scheduleColor = colorScheme.primary;
    }

    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: onTap ?? () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ScheduleDetailScreen(schedule: schedule),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: scheduleColor.withOpacity(0.3),
              width: 2,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Schedule type icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: scheduleColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getIconForType(schedule.type),
                      color: scheduleColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Title and type
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          schedule.title,
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            decoration: schedule.isCompleted 
                              ? TextDecoration.lineThrough 
                              : null,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          schedule.type.displayName,
                          style: textTheme.bodySmall?.copyWith(
                            color: scheduleColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Completion status
                  if (schedule.isCompleted)
                    Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 20,
                    )
                  else
                    IconButton(
                      icon: const Icon(Icons.check_circle_outline),
                      onPressed: () {
                        context.read<ScheduleProvider>().toggleScheduleCompletion(schedule.id);
                      },
                      iconSize: 20,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Description
              if (schedule.description.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Text(
                    schedule.description,
                    style: textTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              
              // Date and time info
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: colorScheme.outline,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDateTime(schedule.startDateTime, schedule.endDateTime, showDate),
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.outline,
                    ),
                  ),
                  
                  // Reminder indicator
                  if (schedule.hasReminder) ...[
                    const SizedBox(width: 16),
                    Icon(
                      Icons.notifications_active,
                      size: 16,
                      color: colorScheme.outline,
                    ),
                  ],
                  
                  // Recurring indicator
                  if (schedule.recurrenceType != RecurrenceType.none) ...[
                    const SizedBox(width: 16),
                    Icon(
                      Icons.repeat,
                      size: 16,
                      color: colorScheme.outline,
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getIconForType(ScheduleType type) {
    switch (type) {
      case ScheduleType.birthday:
        return Icons.cake;
      case ScheduleType.anniversary:
        return Icons.favorite;
      case ScheduleType.holiday:
        return Icons.celebration;
      case ScheduleType.personal:
        return Icons.person;
      case ScheduleType.work:
        return Icons.work;
      case ScheduleType.meeting:
        return Icons.group;
      case ScheduleType.appointment:
        return Icons.event;
      case ScheduleType.reminder:
        return Icons.alarm;
      case ScheduleType.other:
        return Icons.event_note;
    }
  }

  String _formatDateTime(DateTime start, DateTime end, bool showDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final scheduleDate = DateTime(start.year, start.month, start.day);
    
    final timeFormat = DateFormat('HH:mm');
    final dateFormat = DateFormat('MMM dd');
    final fullDateFormat = DateFormat('MMM dd, yyyy');
    
    String timeString = timeFormat.format(start);
    if (start.day != end.day || start.month != end.month || start.year != end.year) {
      // Multi-day event
      timeString = '${timeFormat.format(start)} - ${timeFormat.format(end)}';
    } else if (start.hour != end.hour || start.minute != end.minute) {
      // Same day, different times
      timeString = '${timeFormat.format(start)} - ${timeFormat.format(end)}';
    }
    
    if (!showDate) {
      return timeString;
    }
    
    if (scheduleDate == today) {
      return 'Today, $timeString';
    } else if (scheduleDate == today.add(const Duration(days: 1))) {
      return 'Tomorrow, $timeString';
    } else if (scheduleDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday, $timeString';
    } else if (scheduleDate.year == today.year) {
      return '${dateFormat.format(start)}, $timeString';
    } else {
      return '${fullDateFormat.format(start)}, $timeString';
    }
  }
}
