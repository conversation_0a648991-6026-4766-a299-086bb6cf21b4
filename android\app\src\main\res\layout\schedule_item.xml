<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="center_vertical"
    android:background="?android:attr/selectableItemBackground">

    <!-- Type indicator -->
    <View
        android:id="@+id/type_indicator"
        android:layout_width="4dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="8dp"
        android:background="@color/schedule_personal" />

    <!-- Schedule content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/schedule_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Schedule Title"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary"
            android:maxLines="1"
            android:ellipsize="end" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="2dp">

            <TextView
                android:id="@+id/schedule_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:00"
                android:textSize="12sp"
                android:textColor="@color/widget_text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=" • "
                android:textSize="12sp"
                android:textColor="@color/widget_text_secondary" />

            <TextView
                android:id="@+id/schedule_type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Personal"
                android:textSize="12sp"
                android:textColor="@color/widget_text_secondary"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

    </LinearLayout>

    <!-- Completion status -->
    <ImageView
        android:id="@+id/completion_status"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:src="@drawable/ic_check_circle"
        android:visibility="gone"
        android:contentDescription="Completed" />

</LinearLayout>
