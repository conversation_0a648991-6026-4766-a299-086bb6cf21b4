import '../database/database_helper.dart';
import '../models/schedule.dart';

class ScheduleRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Create
  Future<String> createSchedule(Schedule schedule) async {
    return await _databaseHelper.insertSchedule(schedule);
  }

  // Read
  Future<List<Schedule>> getAllSchedules() async {
    return await _databaseHelper.getAllSchedules();
  }

  Future<Schedule?> getScheduleById(String id) async {
    return await _databaseHelper.getScheduleById(id);
  }

  Future<List<Schedule>> getSchedulesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    return await _databaseHelper.getSchedulesByDateRange(startDate, endDate);
  }

  Future<List<Schedule>> getSchedulesByType(ScheduleType type) async {
    return await _databaseHelper.getSchedulesByType(type);
  }

  Future<List<Schedule>> searchSchedules(String query) async {
    if (query.trim().isEmpty) {
      return await getAllSchedules();
    }
    return await _databaseHelper.searchSchedules(query);
  }

  Future<List<Schedule>> getUpcomingSchedules({int limit = 10}) async {
    return await _databaseHelper.getUpcomingSchedules(limit: limit);
  }

  Future<List<Schedule>> getTodaySchedules() async {
    return await _databaseHelper.getTodaySchedules();
  }

  Future<List<Schedule>> getSchedulesForDate(DateTime date) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);
    return await getSchedulesByDateRange(startOfDay, endOfDay);
  }

  Future<List<Schedule>> getSchedulesForWeek(DateTime weekStart) async {
    final weekEnd = weekStart.add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
    return await getSchedulesByDateRange(weekStart, weekEnd);
  }

  Future<List<Schedule>> getSchedulesForMonth(int year, int month) async {
    final startOfMonth = DateTime(year, month, 1);
    final endOfMonth = DateTime(year, month + 1, 0, 23, 59, 59);
    return await getSchedulesByDateRange(startOfMonth, endOfMonth);
  }

  Future<List<Schedule>> getSchedulesWithReminders() async {
    return await _databaseHelper.getSchedulesWithReminders();
  }

  // Update
  Future<bool> updateSchedule(Schedule schedule) async {
    final result = await _databaseHelper.updateSchedule(schedule);
    return result > 0;
  }

  Future<bool> markScheduleAsCompleted(String id) async {
    final schedule = await getScheduleById(id);
    if (schedule != null) {
      final updatedSchedule = schedule.copyWith(isCompleted: true);
      return await updateSchedule(updatedSchedule);
    }
    return false;
  }

  Future<bool> markScheduleAsIncomplete(String id) async {
    final schedule = await getScheduleById(id);
    if (schedule != null) {
      final updatedSchedule = schedule.copyWith(isCompleted: false);
      return await updateSchedule(updatedSchedule);
    }
    return false;
  }

  // Delete
  Future<bool> deleteSchedule(String id) async {
    final result = await _databaseHelper.deleteSchedule(id);
    return result > 0;
  }

  Future<bool> deleteAllSchedules() async {
    final result = await _databaseHelper.deleteAllSchedules();
    return result > 0;
  }

  // Utility methods
  Future<int> getScheduleCount() async {
    return await _databaseHelper.getScheduleCount();
  }

  Future<Map<ScheduleType, int>> getScheduleCountByType() async {
    final schedules = await getAllSchedules();
    final Map<ScheduleType, int> counts = {};
    
    for (final type in ScheduleType.values) {
      counts[type] = 0;
    }
    
    for (final schedule in schedules) {
      counts[schedule.type] = (counts[schedule.type] ?? 0) + 1;
    }
    
    return counts;
  }

  Future<List<Schedule>> getOverdueSchedules() async {
    final now = DateTime.now();
    final allSchedules = await getAllSchedules();
    
    return allSchedules.where((schedule) {
      return schedule.endDateTime.isBefore(now) && !schedule.isCompleted;
    }).toList();
  }

  // Recurring schedule generation
  List<Schedule> generateRecurringSchedules(
    Schedule baseSchedule,
    DateTime endDate,
  ) {
    final List<Schedule> recurringSchedules = [];
    
    if (baseSchedule.recurrenceType == RecurrenceType.none) {
      return [baseSchedule];
    }

    DateTime currentDate = baseSchedule.startDateTime;
    final Duration duration = baseSchedule.endDateTime.difference(baseSchedule.startDateTime);
    
    while (currentDate.isBefore(endDate) && 
           (baseSchedule.recurrenceEndDate == null || 
            currentDate.isBefore(baseSchedule.recurrenceEndDate!))) {
      
      final newStartDate = currentDate;
      final newEndDate = newStartDate.add(duration);
      
      final recurringSchedule = baseSchedule.copyWith(
        startDateTime: newStartDate,
        endDateTime: newEndDate,
      );
      
      recurringSchedules.add(recurringSchedule);
      
      // Calculate next occurrence
      switch (baseSchedule.recurrenceType) {
        case RecurrenceType.daily:
          currentDate = currentDate.add(const Duration(days: 1));
          break;
        case RecurrenceType.weekly:
          currentDate = currentDate.add(const Duration(days: 7));
          break;
        case RecurrenceType.monthly:
          currentDate = DateTime(
            currentDate.year,
            currentDate.month + 1,
            currentDate.day,
            currentDate.hour,
            currentDate.minute,
          );
          break;
        case RecurrenceType.yearly:
          currentDate = DateTime(
            currentDate.year + 1,
            currentDate.month,
            currentDate.day,
            currentDate.hour,
            currentDate.minute,
          );
          break;
        case RecurrenceType.none:
          break;
      }
    }
    
    return recurringSchedules;
  }

  // Batch operations
  Future<List<String>> createMultipleSchedules(List<Schedule> schedules) async {
    final List<String> ids = [];
    for (final schedule in schedules) {
      final id = await createSchedule(schedule);
      ids.add(id);
    }
    return ids;
  }

  Future<bool> deleteMultipleSchedules(List<String> ids) async {
    bool allDeleted = true;
    for (final id in ids) {
      final result = await deleteSchedule(id);
      if (!result) {
        allDeleted = false;
      }
    }
    return allDeleted;
  }
}
