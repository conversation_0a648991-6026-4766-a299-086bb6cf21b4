import 'dart:convert';
import 'package:home_widget/home_widget.dart';
import '../models/schedule.dart';
import '../repositories/schedule_repository.dart';

class WidgetService {
  static const String _widgetName = 'TestScheduleWidgetProvider';
  static const String _upcomingSchedulesKey = 'upcoming_schedules';
  static const String _lastUpdateKey = 'last_update';

  final ScheduleRepository _repository = ScheduleRepository();

  /// Update the widget with the latest schedule data
  Future<void> updateWidget() async {
    try {
      // Get all schedules and filter for upcoming, uncompleted events
      final allSchedules = await _repository.getAllSchedules();
      final now = DateTime.now();

      // Track recurring events we've already included
      final Map<String, bool> includedRecurringEvents = {};

      // Process schedules to handle recurring events
      final processedSchedules = <Schedule>[];

      for (final schedule in allSchedules) {
        // Skip completed events
        if (schedule.isCompleted) continue;

        // For non-recurring events, just check if they're in the future
        if (schedule.recurrenceType == RecurrenceType.none) {
          if (schedule.startDateTime.isAfter(now)) {
            processedSchedules.add(schedule);
          }
          continue;
        }

        // For recurring events, calculate the next occurrence if needed
        Schedule nextOccurrence = schedule;
        if (!schedule.startDateTime.isAfter(now)) {
          // Original date is in the past, calculate next occurrence
          DateTime nextDate = _calculateNextOccurrence(
            schedule.startDateTime,
            schedule.recurrenceType,
            now,
          );

          // Check if we've reached the recurrence end date
          if (schedule.recurrenceEndDate != null &&
              nextDate.isAfter(schedule.recurrenceEndDate!)) {
            continue; // Skip this event, it has ended
          }

          // Create a new schedule with the updated dates
          final duration = schedule.endDateTime.difference(
            schedule.startDateTime,
          );
          nextOccurrence = schedule.copyWith(
            startDateTime: nextDate,
            endDateTime: nextDate.add(duration),
          );
        }

        // Add to processed schedules if not already included
        if (!includedRecurringEvents.containsKey(schedule.title)) {
          includedRecurringEvents[schedule.title] = true;
          processedSchedules.add(nextOccurrence);
        }
      }

      // Sort by start time
      processedSchedules.sort(
        (a, b) => a.startDateTime.compareTo(b.startDateTime),
      );

      // Limit to 30 events for widget performance
      final limitedSchedules = processedSchedules.take(30).toList();

      // Convert schedules to a format suitable for the widget
      final widgetData = limitedSchedules
          .map(
            (schedule) => {
              'id': schedule.id,
              'title': schedule.title,
              'description': schedule.description,
              'startDateTime': schedule.startDateTime.millisecondsSinceEpoch,
              'endDateTime': schedule.endDateTime.millisecondsSinceEpoch,
              'type': schedule.type.index,
              'colorCode': schedule.colorCode,
              'isCompleted': schedule.isCompleted,
              'year': schedule.startDateTime.year, // Add year explicitly
            },
          )
          .toList();

      // Save data to shared preferences for widget access
      await HomeWidget.saveWidgetData<String>(
        _upcomingSchedulesKey,
        jsonEncode(widgetData),
      );

      // Save last update timestamp
      await HomeWidget.saveWidgetData<String>(
        _lastUpdateKey,
        DateTime.now().millisecondsSinceEpoch.toString(),
      );

      // Update the widget
      await HomeWidget.updateWidget(
        name: _widgetName,
        androidName: _widgetName,
      );
    } catch (e) {
      print('Error updating widget: $e');
    }
  }

  /// Initialize widget configuration
  Future<void> initializeWidget() async {
    try {
      // Set up widget update callback
      HomeWidget.setAppGroupId('group.schedule_manager');

      // Initial widget update
      await updateWidget();
    } catch (e) {
      print('Error initializing widget: $e');
    }
  }

  /// Handle widget tap events
  static Future<void> handleWidgetTap(String? action) async {
    if (action != null) {
      try {
        final uri = Uri.parse(action);
        final scheduleId = uri.queryParameters['schedule_id'];
        final actionType = uri.queryParameters['action'];

        print('Widget tapped - Action: $actionType, Schedule ID: $scheduleId');

        // You can use a global navigator key or event bus to handle navigation
        // For now, we'll just log the action
        if (actionType == 'open_schedule' && scheduleId != null) {
          // Navigate to specific schedule
          print('Opening schedule: $scheduleId');
        } else {
          // Open main app
          print('Opening main app');
        }
      } catch (e) {
        print('Error parsing widget action: $e');
      }
    }
  }

  /// Get formatted schedule data for widget display
  static Map<String, dynamic> formatScheduleForWidget(Schedule schedule) {
    return {
      'id': schedule.id,
      'title': schedule.title,
      'description': schedule.description,
      'startTime': _formatTime(schedule.startDateTime),
      'startDate': _formatDate(schedule.startDateTime),
      'typeIcon': _getTypeIcon(schedule.type),
      'colorCode': schedule.colorCode,
      'isToday': _isToday(schedule.startDateTime),
      'isTomorrow': _isTomorrow(schedule.startDateTime),
      'isCompleted': schedule.isCompleted,
    };
  }

  static String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  static String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final scheduleDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (scheduleDate == today) {
      return 'Today';
    } else if (scheduleDate == today.add(const Duration(days: 1))) {
      return 'Tomorrow';
    } else if (scheduleDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else {
      final months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      // Include year in the date format
      return '${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}';
    }
  }

  static String _getTypeIcon(ScheduleType type) {
    switch (type) {
      case ScheduleType.birthday:
        return '🎂';
      case ScheduleType.anniversary:
        return '💕';
      case ScheduleType.holiday:
        return '🎉';
      case ScheduleType.personal:
        return '👤';
      case ScheduleType.work:
        return '💼';
      case ScheduleType.meeting:
        return '👥';
      case ScheduleType.appointment:
        return '📅';
      case ScheduleType.reminder:
        return '⏰';
      case ScheduleType.other:
        return '📝';
    }
  }

  static bool _isToday(DateTime dateTime) {
    final now = DateTime.now();
    return dateTime.year == now.year &&
        dateTime.month == now.month &&
        dateTime.day == now.day;
  }

  static bool _isTomorrow(DateTime dateTime) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return dateTime.year == tomorrow.year &&
        dateTime.month == tomorrow.month &&
        dateTime.day == tomorrow.day;
  }

  /// Schedule automatic widget updates
  static Future<void> schedulePeriodicUpdates() async {
    // This would typically be handled by the Android system
    // For now, we'll update when the app is opened
    try {
      final service = WidgetService();
      await service.updateWidget();
    } catch (e) {
      print('Error in periodic update: $e');
    }
  }

  /// Clear widget data
  Future<void> clearWidgetData() async {
    try {
      await HomeWidget.saveWidgetData<String>(_upcomingSchedulesKey, '[]');
      await HomeWidget.updateWidget(
        name: _widgetName,
        androidName: _widgetName,
      );
    } catch (e) {
      print('Error clearing widget data: $e');
    }
  }

  /// Get widget configuration
  static Map<String, dynamic> getWidgetConfig() {
    return {
      'name': _widgetName,
      'displayName': 'Schedule Manager',
      'description': 'View your upcoming schedules',
      'supportedSizes': [
        {'width': 2, 'height': 2},
        {'width': 3, 'height': 2},
        {'width': 4, 'height': 2},
        {'width': 3, 'height': 3},
        {'width': 4, 'height': 3},
        {'width': 4, 'height': 4},
      ],
    };
  }

  /// Calculate the next occurrence of a recurring event
  static DateTime _calculateNextOccurrence(
    DateTime lastDate,
    RecurrenceType recurrenceType,
    DateTime after,
  ) {
    DateTime nextDate = lastDate;

    // Keep adding the recurrence interval until we find a date in the future
    while (!nextDate.isAfter(after)) {
      switch (recurrenceType) {
        case RecurrenceType.daily:
          nextDate = nextDate.add(const Duration(days: 1));
          break;
        case RecurrenceType.weekly:
          nextDate = nextDate.add(const Duration(days: 7));
          break;
        case RecurrenceType.monthly:
          // Handle month overflow
          int year = nextDate.year;
          int month = nextDate.month + 1;
          if (month > 12) {
            month = 1;
            year++;
          }
          // Handle day overflow (e.g., Jan 31 -> Feb 28)
          int day = nextDate.day;
          int daysInMonth = DateTime(year, month + 1, 0).day;
          if (day > daysInMonth) {
            day = daysInMonth;
          }
          nextDate = DateTime(year, month, day, nextDate.hour, nextDate.minute);
          break;
        case RecurrenceType.yearly:
          nextDate = DateTime(
            nextDate.year + 1,
            nextDate.month,
            nextDate.day,
            nextDate.hour,
            nextDate.minute,
          );
          break;
        case RecurrenceType.none:
          // Should not happen, but return a date far in the future
          return DateTime(9999);
      }
    }

    return nextDate;
  }
}
