import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/schedule.dart';
import '../providers/schedule_provider.dart';
import 'add_schedule_screen.dart';

class ScheduleDetailScreen extends StatelessWidget {
  final Schedule schedule;

  const ScheduleDetailScreen({super.key, required this.schedule});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    // Parse color from hex string
    Color scheduleColor;
    try {
      scheduleColor = Color(int.parse(schedule.colorCode.replaceFirst('#', '0xFF')));
    } catch (e) {
      scheduleColor = colorScheme.primary;
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Schedule Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => AddScheduleScreen(schedule: schedule),
                ),
              );
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'delete':
                  _showDeleteDialog(context);
                  break;
                case 'duplicate':
                  _duplicateSchedule(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy),
                    SizedBox(width: 8),
                    Text('Duplicate'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Card(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    colors: [
                      scheduleColor.withOpacity(0.1),
                      scheduleColor.withOpacity(0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: scheduleColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            _getIconForType(schedule.type),
                            color: scheduleColor,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                schedule.title,
                                style: textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  decoration: schedule.isCompleted 
                                    ? TextDecoration.lineThrough 
                                    : null,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                schedule.type.displayName,
                                style: textTheme.bodyMedium?.copyWith(
                                  color: scheduleColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Completion toggle
                        Consumer<ScheduleProvider>(
                          builder: (context, provider, child) {
                            return IconButton(
                              icon: Icon(
                                schedule.isCompleted 
                                  ? Icons.check_circle 
                                  : Icons.check_circle_outline,
                                color: schedule.isCompleted 
                                  ? Colors.green 
                                  : colorScheme.outline,
                                size: 28,
                              ),
                              onPressed: () {
                                provider.toggleScheduleCompletion(schedule.id);
                              },
                            );
                          },
                        ),
                      ],
                    ),
                    if (schedule.description.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Text(
                        schedule.description,
                        style: textTheme.bodyLarge,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),

            // Date & Time Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date & Time',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      Icons.calendar_today,
                      'Start',
                      DateFormat('EEEE, MMM dd, yyyy').format(schedule.startDateTime),
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow(
                      Icons.access_time,
                      'Time',
                      '${DateFormat('HH:mm').format(schedule.startDateTime)} - ${DateFormat('HH:mm').format(schedule.endDateTime)}',
                    ),
                    if (schedule.startDateTime.day != schedule.endDateTime.day ||
                        schedule.startDateTime.month != schedule.endDateTime.month ||
                        schedule.startDateTime.year != schedule.endDateTime.year) ...[
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        Icons.event,
                        'End Date',
                        DateFormat('EEEE, MMM dd, yyyy').format(schedule.endDateTime),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Additional Info Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Additional Information',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    // Reminder
                    _buildInfoRow(
                      Icons.notifications,
                      'Reminder',
                      schedule.hasReminder && schedule.reminderDateTime != null
                        ? DateFormat('MMM dd, yyyy HH:mm').format(schedule.reminderDateTime!)
                        : 'No reminder set',
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Recurrence
                    _buildInfoRow(
                      Icons.repeat,
                      'Recurrence',
                      _getRecurrenceText(schedule.recurrenceType),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Status
                    _buildInfoRow(
                      Icons.check_circle,
                      'Status',
                      schedule.isCompleted ? 'Completed' : 'Pending',
                      valueColor: schedule.isCompleted ? Colors.green : Colors.orange,
                    ),
                  ],
                ),
              ),
            ),

            // Notes Card (if notes exist)
            if (schedule.notes != null && schedule.notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notes',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        schedule.notes!,
                        style: textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Metadata Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Metadata',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      Icons.add,
                      'Created',
                      DateFormat('MMM dd, yyyy HH:mm').format(schedule.createdAt),
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow(
                      Icons.edit,
                      'Last Updated',
                      DateFormat('MMM dd, yyyy HH:mm').format(schedule.updatedAt),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, {Color? valueColor}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  color: valueColor ?? Colors.grey[700],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getIconForType(ScheduleType type) {
    switch (type) {
      case ScheduleType.birthday:
        return Icons.cake;
      case ScheduleType.anniversary:
        return Icons.favorite;
      case ScheduleType.holiday:
        return Icons.celebration;
      case ScheduleType.personal:
        return Icons.person;
      case ScheduleType.work:
        return Icons.work;
      case ScheduleType.meeting:
        return Icons.group;
      case ScheduleType.appointment:
        return Icons.event;
      case ScheduleType.reminder:
        return Icons.alarm;
      case ScheduleType.other:
        return Icons.event_note;
    }
  }

  String _getRecurrenceText(RecurrenceType type) {
    switch (type) {
      case RecurrenceType.none:
        return 'No recurrence';
      case RecurrenceType.daily:
        return 'Daily';
      case RecurrenceType.weekly:
        return 'Weekly';
      case RecurrenceType.monthly:
        return 'Monthly';
      case RecurrenceType.yearly:
        return 'Yearly';
    }
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Schedule'),
        content: Text('Are you sure you want to delete "${schedule.title}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop(); // Close dialog
              final success = await context.read<ScheduleProvider>().deleteSchedule(schedule.id);
              if (success && context.mounted) {
                Navigator.of(context).pop(); // Go back to previous screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Schedule deleted successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _duplicateSchedule(BuildContext context) {
    final duplicatedSchedule = schedule.copyWith(
      id: null, // This will generate a new ID
      title: '${schedule.title} (Copy)',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddScheduleScreen(schedule: duplicatedSchedule),
      ),
    );
  }
}
