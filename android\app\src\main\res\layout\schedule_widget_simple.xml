<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="#FFFFFF">

    <!-- Widget Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Schedule Manager"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="#212121"
        android:gravity="center"
        android:paddingBottom="8dp" />

    <!-- Content -->
    <LinearLayout
        android:id="@+id/widget_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- First Event -->
        <LinearLayout
            android:id="@+id/event_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:id="@+id/event_1_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:00"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#212121"
                android:minWidth="45dp" />

            <View
                android:id="@+id/event_1_indicator"
                android:layout_width="4dp"
                android:layout_height="20dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#45B7D1" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_1_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Meeting with team"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_1_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Work"
                    android:textSize="10sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end" />

            </LinearLayout>

        </LinearLayout>

        <!-- Second Event -->
        <LinearLayout
            android:id="@+id/event_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="?android:attr/selectableItemBackground"
            android:visibility="gone">

            <TextView
                android:id="@+id/event_2_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="14:00"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#212121"
                android:minWidth="45dp" />

            <View
                android:id="@+id/event_2_indicator"
                android:layout_width="4dp"
                android:layout_height="16dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FF6B6B" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_2_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Doctor appointment"
                    android:textSize="11sp"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_2_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Personal"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end" />

            </LinearLayout>

        </LinearLayout>

        <!-- More Events Indicator -->
        <TextView
            android:id="@+id/more_events_indicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="+ 3 more events"
            android:textSize="9sp"
            android:textColor="#757575"
            android:gravity="center"
            android:padding="4dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Empty State -->
    <TextView
        android:id="@+id/empty_message"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="No upcoming events"
        android:textSize="12sp"
        android:textColor="#757575"
        android:gravity="center"
        android:visibility="gone" />

</LinearLayout>
