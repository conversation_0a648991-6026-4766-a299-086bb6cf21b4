import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/schedule_provider.dart';
import '../models/schedule.dart';

class QuickStatsWidget extends StatelessWidget {
  const QuickStatsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ScheduleProvider>(
      builder: (context, provider, child) {
        return FutureBuilder<List<dynamic>>(
          future: Future.wait([
            provider.getTodaySchedules(),
            provider.getUpcomingSchedules(limit: 100),
            provider.getOverdueSchedules(),
          ]),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(child: CircularProgressIndicator()),
                ),
              );
            }

            if (snapshot.hasError) {
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Error loading stats: ${snapshot.error}',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              );
            }

            final results = snapshot.data ?? [];
            final todaySchedules = results.isNotEmpty
                ? results[0] as List<Schedule>
                : <Schedule>[];
            final upcomingSchedules = results.length > 1
                ? results[1] as List<Schedule>
                : <Schedule>[];
            final overdueSchedules = results.length > 2
                ? results[2] as List<Schedule>
                : <Schedule>[];

            final todayCompleted = todaySchedules
                .where((s) => s.isCompleted)
                .length;
            final todayTotal = todaySchedules.length;

            return Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quick Stats',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Stats grid
                    Row(
                      children: [
                        Expanded(
                          child: _StatItem(
                            icon: Icons.today,
                            label: 'Today',
                            value: '$todayCompleted/$todayTotal',
                            color: Colors.blue,
                            subtitle: 'Completed',
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _StatItem(
                            icon: Icons.upcoming,
                            label: 'Upcoming',
                            value: '${upcomingSchedules.length}',
                            color: Colors.green,
                            subtitle: 'Events',
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    Row(
                      children: [
                        Expanded(
                          child: _StatItem(
                            icon: Icons.warning,
                            label: 'Overdue',
                            value: '${overdueSchedules.length}',
                            color: Colors.red,
                            subtitle: 'Items',
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _StatItem(
                            icon: Icons.event_note,
                            label: 'Total',
                            value: '${provider.allSchedules.length}',
                            color: Colors.purple,
                            subtitle: 'Schedules',
                          ),
                        ),
                      ],
                    ),

                    // Progress bar for today's completion
                    if (todayTotal > 0) ...[
                      const SizedBox(height: 16),
                      Text(
                        'Today\'s Progress',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: todayTotal > 0 ? todayCompleted / todayTotal : 0,
                        backgroundColor: Theme.of(
                          context,
                        ).colorScheme.outline.withOpacity(0.2),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          todayCompleted == todayTotal
                              ? Colors.green
                              : Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${((todayCompleted / todayTotal) * 100).round()}% completed',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.outline,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class _StatItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;
  final String subtitle;

  const _StatItem({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
        ],
      ),
    );
  }
}
