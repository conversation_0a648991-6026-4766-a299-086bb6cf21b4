import 'package:flutter/foundation.dart';
import '../models/schedule.dart';
import '../repositories/schedule_repository.dart';
import '../services/widget_service.dart';

class ScheduleProvider extends ChangeNotifier {
  final ScheduleRepository _repository = ScheduleRepository();
  final WidgetService _widgetService = WidgetService();

  List<Schedule> _schedules = [];
  List<Schedule> _filteredSchedules = [];
  bool _isLoading = false;
  String _searchQuery = '';
  ScheduleType? _selectedType;
  DateTime _selectedDate = DateTime.now();
  String? _errorMessage;

  // Getters
  List<Schedule> get schedules => _filteredSchedules;
  List<Schedule> get allSchedules => _schedules;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  ScheduleType? get selectedType => _selectedType;
  DateTime get selectedDate => _selectedDate;
  String? get errorMessage => _errorMessage;

  // Initialize and load schedules
  Future<void> loadSchedules() async {
    _setLoading(true);
    _clearError();

    try {
      _schedules = await _repository.getAllSchedules();
      _applyFilters();
    } catch (e) {
      _setError('Failed to load schedules: $e');
    } finally {
      _setLoading(false);
    }
  }

  // CRUD Operations
  Future<bool> createSchedule(Schedule schedule) async {
    _setLoading(true);
    _clearError();

    try {
      await _repository.createSchedule(schedule);
      await loadSchedules(); // Reload to get updated list
      _widgetService.updateWidget(); // Update widget
      return true;
    } catch (e) {
      _setError('Failed to create schedule: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateSchedule(Schedule schedule) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _repository.updateSchedule(schedule);
      if (success) {
        await loadSchedules(); // Reload to get updated list
        _widgetService.updateWidget(); // Update widget
      }
      return success;
    } catch (e) {
      _setError('Failed to update schedule: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteSchedule(String id) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _repository.deleteSchedule(id);
      if (success) {
        await loadSchedules(); // Reload to get updated list
        _widgetService.updateWidget(); // Update widget
      }
      return success;
    } catch (e) {
      _setError('Failed to delete schedule: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> toggleScheduleCompletion(String id) async {
    try {
      final schedule = _schedules.firstWhere((s) => s.id == id);
      final updatedSchedule = schedule.copyWith(
        isCompleted: !schedule.isCompleted,
      );
      return await updateSchedule(updatedSchedule);
    } catch (e) {
      _setError('Failed to toggle schedule completion: $e');
      return false;
    }
  }

  // Filtering and searching
  void setSearchQuery(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  void setSelectedType(ScheduleType? type) {
    _selectedType = type;
    _applyFilters();
    notifyListeners();
  }

  void setSelectedDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _selectedType = null;
    _applyFilters();
    notifyListeners();
  }

  void _applyFilters() {
    _filteredSchedules = _schedules.where((schedule) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!schedule.title.toLowerCase().contains(query) &&
            !schedule.description.toLowerCase().contains(query) &&
            !(schedule.notes?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // Type filter
      if (_selectedType != null && schedule.type != _selectedType) {
        return false;
      }

      return true;
    }).toList();

    // Sort by start date
    _filteredSchedules.sort(
      (a, b) => a.startDateTime.compareTo(b.startDateTime),
    );
  }

  // Date-specific methods
  Future<List<Schedule>> getSchedulesForDate(DateTime date) async {
    try {
      return await _repository.getSchedulesForDate(date);
    } catch (e) {
      _setError('Failed to get schedules for date: $e');
      return [];
    }
  }

  Future<List<Schedule>> getSchedulesForWeek(DateTime weekStart) async {
    try {
      return await _repository.getSchedulesForWeek(weekStart);
    } catch (e) {
      _setError('Failed to get schedules for week: $e');
      return [];
    }
  }

  Future<List<Schedule>> getSchedulesForMonth(int year, int month) async {
    try {
      return await _repository.getSchedulesForMonth(year, month);
    } catch (e) {
      _setError('Failed to get schedules for month: $e');
      return [];
    }
  }

  Future<List<Schedule>> getUpcomingSchedules({int limit = 10}) async {
    try {
      return await _repository.getUpcomingSchedules(limit: limit);
    } catch (e) {
      _setError('Failed to get upcoming schedules: $e');
      return [];
    }
  }

  Future<List<Schedule>> getTodaySchedules() async {
    try {
      return await _repository.getTodaySchedules();
    } catch (e) {
      _setError('Failed to get today\'s schedules: $e');
      return [];
    }
  }

  // Statistics
  Future<Map<ScheduleType, int>> getScheduleCountByType() async {
    try {
      return await _repository.getScheduleCountByType();
    } catch (e) {
      _setError('Failed to get schedule count by type: $e');
      return {};
    }
  }

  Future<List<Schedule>> getOverdueSchedules() async {
    try {
      return await _repository.getOverdueSchedules();
    } catch (e) {
      _setError('Failed to get overdue schedules: $e');
      return [];
    }
  }

  // Recurring schedules
  Future<bool> createRecurringSchedule(
    Schedule baseSchedule,
    DateTime endDate,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      final recurringSchedules = _repository.generateRecurringSchedules(
        baseSchedule,
        endDate,
      );

      await _repository.createMultipleSchedules(recurringSchedules);
      await loadSchedules();
      return true;
    } catch (e) {
      _setError('Failed to create recurring schedule: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Utility methods
  List<Schedule> getSchedulesForDateRange(DateTime start, DateTime end) {
    return _schedules.where((schedule) {
      return schedule.startDateTime.isAfter(
            start.subtract(const Duration(days: 1)),
          ) &&
          schedule.startDateTime.isBefore(end.add(const Duration(days: 1)));
    }).toList();
  }

  bool hasSchedulesOnDate(DateTime date) {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

    return _schedules.any((schedule) {
      return schedule.startDateTime.isAfter(
            startOfDay.subtract(const Duration(seconds: 1)),
          ) &&
          schedule.startDateTime.isBefore(
            endOfDay.add(const Duration(seconds: 1)),
          );
    });
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Refresh data
  Future<void> refresh() async {
    await loadSchedules();
  }
}
