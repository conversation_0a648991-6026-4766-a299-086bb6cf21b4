import 'package:uuid/uuid.dart';

enum ScheduleType {
  birthday,
  anniversary,
  holiday,
  personal,
  work,
  meeting,
  appointment,
  reminder,
  other,
}

enum RecurrenceType { none, daily, weekly, monthly, yearly }

class Schedule {
  final String id;
  final String title;
  final String description;
  final DateTime startDateTime;
  final DateTime endDateTime;
  final ScheduleType type;
  final String? notes;
  final bool hasReminder;
  final DateTime? reminderDateTime;
  final RecurrenceType recurrenceType;
  final DateTime? recurrenceEndDate;
  final String colorCode;
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  Schedule({
    String? id,
    required this.title,
    required this.description,
    required this.startDateTime,
    required this.endDateTime,
    required this.type,
    this.notes,
    this.hasReminder = false,
    this.reminderDateTime,
    this.recurrenceType = RecurrenceType.none,
    this.recurrenceEndDate,
    String? colorCode,
    this.isCompleted = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       colorCode = colorCode ?? _getDefaultColorForType(type),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  static String _getDefaultColorForType(ScheduleType type) {
    switch (type) {
      case ScheduleType.birthday:
        return '#FF6B6B';
      case ScheduleType.anniversary:
        return '#FF8E8E';
      case ScheduleType.holiday:
        return '#4ECDC4';
      case ScheduleType.personal:
        return '#45B7D1';
      case ScheduleType.work:
        return '#96CEB4';
      case ScheduleType.meeting:
        return '#FFEAA7';
      case ScheduleType.appointment:
        return '#DDA0DD';
      case ScheduleType.reminder:
        return '#FFB347';
      case ScheduleType.other:
        return '#A8A8A8';
    }
  }

  Schedule copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? startDateTime,
    DateTime? endDateTime,
    ScheduleType? type,
    String? notes,
    bool? hasReminder,
    DateTime? reminderDateTime,
    RecurrenceType? recurrenceType,
    DateTime? recurrenceEndDate,
    String? colorCode,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Schedule(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      startDateTime: startDateTime ?? this.startDateTime,
      endDateTime: endDateTime ?? this.endDateTime,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      hasReminder: hasReminder ?? this.hasReminder,
      reminderDateTime: reminderDateTime ?? this.reminderDateTime,
      recurrenceType: recurrenceType ?? this.recurrenceType,
      recurrenceEndDate: recurrenceEndDate ?? this.recurrenceEndDate,
      colorCode: colorCode ?? this.colorCode,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'start_date_time': startDateTime.millisecondsSinceEpoch,
      'end_date_time': endDateTime.millisecondsSinceEpoch,
      'type': type.index,
      'notes': notes,
      'has_reminder': hasReminder ? 1 : 0,
      'reminder_date_time': reminderDateTime?.millisecondsSinceEpoch,
      'recurrence_type': recurrenceType.index,
      'recurrence_end_date': recurrenceEndDate?.millisecondsSinceEpoch,
      'color_code': colorCode,
      'is_completed': isCompleted ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory Schedule.fromMap(Map<String, dynamic> map) {
    return Schedule(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      startDateTime: DateTime.fromMillisecondsSinceEpoch(
        map['start_date_time'],
      ),
      endDateTime: DateTime.fromMillisecondsSinceEpoch(map['end_date_time']),
      type: ScheduleType.values[map['type']],
      notes: map['notes'],
      hasReminder: map['has_reminder'] == 1,
      reminderDateTime: map['reminder_date_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['reminder_date_time'])
          : null,
      recurrenceType: RecurrenceType.values[map['recurrence_type']],
      recurrenceEndDate: map['recurrence_end_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['recurrence_end_date'])
          : null,
      colorCode: map['color_code'],
      isCompleted: map['is_completed'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  @override
  String toString() {
    return 'Schedule{id: $id, title: $title, startDateTime: $startDateTime, type: $type}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Schedule && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

extension ScheduleTypeExtension on ScheduleType {
  String get displayName {
    switch (this) {
      case ScheduleType.birthday:
        return 'Birthday';
      case ScheduleType.anniversary:
        return 'Anniversary';
      case ScheduleType.holiday:
        return 'Holiday';
      case ScheduleType.personal:
        return 'Personal';
      case ScheduleType.work:
        return 'Work';
      case ScheduleType.meeting:
        return 'Meeting';
      case ScheduleType.appointment:
        return 'Appointment';
      case ScheduleType.reminder:
        return 'Reminder';
      case ScheduleType.other:
        return 'Other';
    }
  }

  String get iconName {
    switch (this) {
      case ScheduleType.birthday:
        return 'cake';
      case ScheduleType.anniversary:
        return 'favorite';
      case ScheduleType.holiday:
        return 'celebration';
      case ScheduleType.personal:
        return 'person';
      case ScheduleType.work:
        return 'work';
      case ScheduleType.meeting:
        return 'group';
      case ScheduleType.appointment:
        return 'event';
      case ScheduleType.reminder:
        return 'alarm';
      case ScheduleType.other:
        return 'event_note';
    }
  }
}

extension RecurrenceTypeExtension on RecurrenceType {
  String get displayName {
    switch (this) {
      case RecurrenceType.none:
        return 'Never';
      case RecurrenceType.daily:
        return 'Daily';
      case RecurrenceType.weekly:
        return 'Weekly';
      case RecurrenceType.monthly:
        return 'Monthly';
      case RecurrenceType.yearly:
        return 'Yearly';
    }
  }

  String get description {
    switch (this) {
      case RecurrenceType.none:
        return 'This event will not repeat';
      case RecurrenceType.daily:
        return 'This event will repeat every day';
      case RecurrenceType.weekly:
        return 'This event will repeat every week';
      case RecurrenceType.monthly:
        return 'This event will repeat every month';
      case RecurrenceType.yearly:
        return 'This event will repeat every year';
    }
  }
}
