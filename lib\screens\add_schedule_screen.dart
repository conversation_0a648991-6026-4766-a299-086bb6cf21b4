import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/schedule_provider.dart';
import '../models/schedule.dart';

class AddScheduleScreen extends StatefulWidget {
  final Schedule? schedule; // For editing existing schedule

  const AddScheduleScreen({super.key, this.schedule});

  @override
  State<AddScheduleScreen> createState() => _AddScheduleScreenState();
}

class _AddScheduleScreenState extends State<AddScheduleScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _startDate = DateTime.now();
  TimeOfDay _startTime = TimeOfDay.now();
  DateTime _endDate = DateTime.now();
  TimeOfDay _endTime = TimeOfDay.now().replacing(
    hour: TimeOfDay.now().hour + 1,
  );

  ScheduleType _selectedType = ScheduleType.personal;
  bool _hasReminder = false;
  DateTime? _reminderDateTime;
  RecurrenceType _recurrenceType = RecurrenceType.none;
  DateTime? _recurrenceEndDate;
  String _colorCode = '#45B7D1';

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.schedule != null) {
      _populateFields(widget.schedule!);
    } else {
      // Set default end time to 1 hour after start time
      final now = DateTime.now();
      _startDate = DateTime(now.year, now.month, now.day);
      _endDate = _startDate;
      _startTime = TimeOfDay.fromDateTime(now);
      _endTime = TimeOfDay.fromDateTime(now.add(const Duration(hours: 1)));
    }
  }

  void _populateFields(Schedule schedule) {
    _titleController.text = schedule.title;
    _descriptionController.text = schedule.description;
    _notesController.text = schedule.notes ?? '';

    _startDate = DateTime(
      schedule.startDateTime.year,
      schedule.startDateTime.month,
      schedule.startDateTime.day,
    );
    _startTime = TimeOfDay.fromDateTime(schedule.startDateTime);

    _endDate = DateTime(
      schedule.endDateTime.year,
      schedule.endDateTime.month,
      schedule.endDateTime.day,
    );
    _endTime = TimeOfDay.fromDateTime(schedule.endDateTime);

    _selectedType = schedule.type;
    _hasReminder = schedule.hasReminder;
    _reminderDateTime = schedule.reminderDateTime;
    _recurrenceType = schedule.recurrenceType;
    _recurrenceEndDate = schedule.recurrenceEndDate;
    _colorCode = schedule.colorCode;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.schedule != null ? 'Edit Schedule' : 'Add Schedule'),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(onPressed: _saveSchedule, child: const Text('Save')),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Title *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description *',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Schedule Type
              DropdownButtonFormField<ScheduleType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Type',
                  border: OutlineInputBorder(),
                ),
                items: ScheduleType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Row(
                      children: [
                        Icon(_getIconForType(type)),
                        const SizedBox(width: 8),
                        Text(type.displayName),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                      // Set default color based on type
                      switch (value) {
                        case ScheduleType.birthday:
                          _colorCode = '#FF6B6B';
                          break;
                        case ScheduleType.anniversary:
                          _colorCode = '#FF8E8E';
                          break;
                        case ScheduleType.holiday:
                          _colorCode = '#4ECDC4';
                          break;
                        case ScheduleType.personal:
                          _colorCode = '#45B7D1';
                          break;
                        case ScheduleType.work:
                          _colorCode = '#96CEB4';
                          break;
                        case ScheduleType.meeting:
                          _colorCode = '#FFEAA7';
                          break;
                        case ScheduleType.appointment:
                          _colorCode = '#DDA0DD';
                          break;
                        case ScheduleType.reminder:
                          _colorCode = '#FFB347';
                          break;
                        case ScheduleType.other:
                          _colorCode = '#A8A8A8';
                          break;
                      }
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // Date and Time Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date & Time',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      // Start Date and Time
                      Row(
                        children: [
                          Expanded(
                            child: ListTile(
                              title: const Text('Start Date'),
                              subtitle: Text(
                                DateFormat('MMM dd, yyyy').format(_startDate),
                              ),
                              leading: const Icon(Icons.calendar_today),
                              onTap: () => _selectDate(context, true),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          Expanded(
                            child: ListTile(
                              title: const Text('Start Time'),
                              subtitle: Text(_startTime.format(context)),
                              leading: const Icon(Icons.access_time),
                              onTap: () => _selectTime(context, true),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                        ],
                      ),

                      const Divider(),

                      // End Date and Time
                      Row(
                        children: [
                          Expanded(
                            child: ListTile(
                              title: const Text('End Date'),
                              subtitle: Text(
                                DateFormat('MMM dd, yyyy').format(_endDate),
                              ),
                              leading: const Icon(Icons.calendar_today),
                              onTap: () => _selectDate(context, false),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          Expanded(
                            child: ListTile(
                              title: const Text('End Time'),
                              subtitle: Text(_endTime.format(context)),
                              leading: const Icon(Icons.access_time),
                              onTap: () => _selectTime(context, false),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Reminder Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SwitchListTile(
                        title: const Text('Set Reminder'),
                        subtitle: _hasReminder && _reminderDateTime != null
                            ? Text(
                                DateFormat(
                                  'MMM dd, yyyy HH:mm',
                                ).format(_reminderDateTime!),
                              )
                            : const Text('No reminder set'),
                        value: _hasReminder,
                        onChanged: (value) {
                          setState(() {
                            _hasReminder = value;
                            if (value && _reminderDateTime == null) {
                              // Set default reminder to 30 minutes before start
                              final startDateTime = DateTime(
                                _startDate.year,
                                _startDate.month,
                                _startDate.day,
                                _startTime.hour,
                                _startTime.minute,
                              );
                              _reminderDateTime = startDateTime.subtract(
                                const Duration(minutes: 30),
                              );
                            }
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                      if (_hasReminder)
                        ListTile(
                          title: const Text('Reminder Time'),
                          subtitle: _reminderDateTime != null
                              ? Text(
                                  DateFormat(
                                    'MMM dd, yyyy HH:mm',
                                  ).format(_reminderDateTime!),
                                )
                              : const Text('Tap to set'),
                          leading: const Icon(Icons.notifications),
                          onTap: _selectReminderDateTime,
                          contentPadding: EdgeInsets.zero,
                        ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Recurrence Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Recurrence',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      // Recurrence Type
                      DropdownButtonFormField<RecurrenceType>(
                        value: _recurrenceType,
                        decoration: const InputDecoration(
                          labelText: 'Repeat',
                          border: OutlineInputBorder(),
                        ),
                        items: RecurrenceType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Row(
                              children: [
                                Icon(_getIconForRecurrence(type)),
                                const SizedBox(width: 8),
                                Text(type.displayName),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _recurrenceType = value ?? RecurrenceType.none;
                            // Clear end date if recurrence is set to none
                            if (_recurrenceType == RecurrenceType.none) {
                              _recurrenceEndDate = null;
                            }
                          });
                        },
                      ),

                      // Recurrence End Date (only show if recurrence is not none)
                      if (_recurrenceType != RecurrenceType.none) ...[
                        const SizedBox(height: 16),
                        ListTile(
                          title: const Text('End Repeat'),
                          subtitle: _recurrenceEndDate != null
                              ? Text(
                                  DateFormat(
                                    'MMM dd, yyyy',
                                  ).format(_recurrenceEndDate!),
                                )
                              : const Text('Never (tap to set end date)'),
                          leading: const Icon(Icons.event_busy),
                          onTap: _selectRecurrenceEndDate,
                          contentPadding: EdgeInsets.zero,
                          trailing: _recurrenceEndDate != null
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    setState(() {
                                      _recurrenceEndDate = null;
                                    });
                                  },
                                )
                              : null,
                        ),
                        if (_recurrenceType != RecurrenceType.none)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              _getRecurrenceDescription(),
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                                  ),
                            ),
                          ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Notes
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getIconForType(ScheduleType type) {
    switch (type) {
      case ScheduleType.birthday:
        return Icons.cake;
      case ScheduleType.anniversary:
        return Icons.favorite;
      case ScheduleType.holiday:
        return Icons.celebration;
      case ScheduleType.personal:
        return Icons.person;
      case ScheduleType.work:
        return Icons.work;
      case ScheduleType.meeting:
        return Icons.group;
      case ScheduleType.appointment:
        return Icons.event;
      case ScheduleType.reminder:
        return Icons.alarm;
      case ScheduleType.other:
        return Icons.event_note;
    }
  }

  IconData _getIconForRecurrence(RecurrenceType type) {
    switch (type) {
      case RecurrenceType.none:
        return Icons.event;
      case RecurrenceType.daily:
        return Icons.today;
      case RecurrenceType.weekly:
        return Icons.view_week;
      case RecurrenceType.monthly:
        return Icons.calendar_month;
      case RecurrenceType.yearly:
        return Icons.calendar_today;
    }
  }

  String _getRecurrenceDescription() {
    if (_recurrenceType == RecurrenceType.none) {
      return 'This event will not repeat';
    }

    String description = _recurrenceType.description;

    if (_recurrenceEndDate != null) {
      description +=
          ' until ${DateFormat('MMM dd, yyyy').format(_recurrenceEndDate!)}';
    }

    return description;
  }

  Future<void> _selectDate(BuildContext context, bool isStart) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStart ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      setState(() {
        if (isStart) {
          _startDate = picked;
          // If start date is after end date, update end date
          if (_startDate.isAfter(_endDate)) {
            _endDate = _startDate;
          }
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _selectTime(BuildContext context, bool isStart) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStart ? _startTime : _endTime,
    );
    if (picked != null) {
      setState(() {
        if (isStart) {
          _startTime = picked;
        } else {
          _endTime = picked;
        }
      });
    }
  }

  Future<void> _selectReminderDateTime() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _reminderDateTime ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2030),
    );

    if (pickedDate != null && mounted) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: _reminderDateTime != null
            ? TimeOfDay.fromDateTime(_reminderDateTime!)
            : TimeOfDay.now(),
      );

      if (pickedTime != null && mounted) {
        setState(() {
          _reminderDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );
        });
      }
    }
  }

  Future<void> _selectRecurrenceEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _recurrenceEndDate ?? _startDate.add(const Duration(days: 365)),
      firstDate: _startDate,
      lastDate: DateTime(2030),
    );

    if (picked != null && mounted) {
      setState(() {
        _recurrenceEndDate = picked;
      });
    }
  }

  Future<void> _saveSchedule() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final startDateTime = DateTime(
        _startDate.year,
        _startDate.month,
        _startDate.day,
        _startTime.hour,
        _startTime.minute,
      );

      final endDateTime = DateTime(
        _endDate.year,
        _endDate.month,
        _endDate.day,
        _endTime.hour,
        _endTime.minute,
      );

      // Validate that end time is after start time
      if (endDateTime.isBefore(startDateTime)) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('End time must be after start time'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final schedule = Schedule(
        id: widget.schedule?.id,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        startDateTime: startDateTime,
        endDateTime: endDateTime,
        type: _selectedType,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        hasReminder: _hasReminder,
        reminderDateTime: _hasReminder ? _reminderDateTime : null,
        recurrenceType: _recurrenceType,
        recurrenceEndDate: _recurrenceEndDate,
        colorCode: _colorCode,
      );

      final provider = context.read<ScheduleProvider>();
      bool success;

      if (widget.schedule != null) {
        success = await provider.updateSchedule(schedule);
      } else {
        success = await provider.createSchedule(schedule);
      }

      if (success) {
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.schedule != null
                    ? 'Schedule updated successfully'
                    : 'Schedule created successfully',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(provider.errorMessage ?? 'Failed to save schedule'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
