<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="6dp"
    android:gravity="center_vertical"
    android:background="?android:attr/selectableItemBackground">

    <!-- Time -->
    <TextView
        android:id="@+id/item_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="10:00"
        android:textSize="11sp"
        android:textStyle="bold"
        android:textColor="@color/widget_text_primary"
        android:minWidth="40dp"
        android:gravity="center" />

    <!-- Type indicator dot -->
    <View
        android:id="@+id/item_type_dot"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/type_indicator_dot" />

    <!-- Content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/item_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Schedule Title"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/item_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Personal"
            android:textSize="10sp"
            android:textColor="@color/widget_text_secondary"
            android:maxLines="1"
            android:ellipsize="end" />

    </LinearLayout>

    <!-- Status indicator -->
    <ImageView
        android:id="@+id/item_status"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginStart="4dp"
        android:src="@drawable/ic_check_circle_small"
        android:visibility="gone"
        android:contentDescription="Completed" />

</LinearLayout>
