<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="@drawable/widget_background">

    <!-- Widget Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="8dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_schedule"
            android:contentDescription="Schedule Icon" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Upcoming Schedules"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary"
            android:paddingStart="8dp"
            android:paddingEnd="8dp" />

        <TextView
            android:id="@+id/last_update"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="10sp"
            android:textColor="@color/widget_text_secondary" />

    </LinearLayout>

    <!-- Schedules List -->
    <LinearLayout
        android:id="@+id/schedule_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- First Schedule Item -->
        <LinearLayout
            android:id="@+id/schedule_item_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/item_time_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:00"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="@color/widget_text_primary"
                android:minWidth="40dp" />

            <View
                android:id="@+id/item_type_dot_1"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/type_indicator_dot" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/item_title_1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Schedule Title"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/widget_text_primary"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/item_type_1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Personal"
                    android:textSize="10sp"
                    android:textColor="@color/widget_text_secondary"
                    android:maxLines="1"
                    android:ellipsize="end" />

            </LinearLayout>

        </LinearLayout>

        <!-- More items indicator -->
        <TextView
            android:id="@+id/more_items_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="+ 2 more events"
            android:textSize="10sp"
            android:textColor="@color/widget_text_secondary"
            android:gravity="center"
            android:padding="4dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/empty_state"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_event_available"
            android:alpha="0.5"
            android:contentDescription="No Events" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No upcoming schedules"
            android:textSize="12sp"
            android:textColor="@color/widget_text_secondary"
            android:paddingTop="8dp" />

    </LinearLayout>

</LinearLayout>
